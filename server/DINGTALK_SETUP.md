# 钉钉通知配置指南

本文档介绍如何配置和使用参数管理系统的钉钉通知功能。

## 🚀 功能特性

- ✅ 客户端上线/离线通知
- ✅ 支持Markdown格式消息
- ✅ 支持钉钉机器人签名验证
- ✅ 客户端上线通知开关控制
- ✅ Web界面配置管理
- ✅ 测试通知功能

## 📋 前置条件

### 1. 创建钉钉机器人

1. 在钉钉群中，点击群设置 → 智能群助手 → 添加机器人
2. 选择"自定义"机器人
3. 设置机器人名称和头像
4. 安全设置选择"加签"（推荐）或"自定义关键词"
5. 复制Webhook地址和密钥

### 2. 获取配置信息

配置完成后，您将获得：
- **Webhook URL**: `https://oapi.dingtalk.com/robot/send?access_token=xxx`
- **密钥**: 用于签名验证的密钥字符串（如果选择了加签）

## ⚙️ 配置步骤

### 方法一：Web界面配置（推荐）

1. 启动服务器后，访问 `http://localhost:8000`
2. 点击"配置管理"按钮
3. 在"钉钉通知配置"部分填写：
   - **启用钉钉通知**: 选择"启用"
   - **Webhook URL**: 粘贴钉钉机器人的Webhook地址
   - **密钥**: 粘贴钉钉机器人的密钥（可选）
   - **客户端上线通知**: 使用滑动块控制是否启用
4. 点击"测试钉钉通知"验证配置
5. 点击"保存配置"

### 方法二：环境变量配置

编辑 `.env` 文件：

```bash
# 钉钉通知配置
DINGTALK_ENABLED=true
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=your_token
DINGTALK_SECRET=your_secret_key
DINGTALK_CLIENT_ONLINE_ENABLED=true
```

## 🧪 测试配置

### 1. Web界面测试

在配置页面点击"测试钉钉通知"按钮，如果配置正确，您将在钉钉群中收到测试消息。

### 2. 命令行测试

```bash
cd server
python test_dingtalk_integration.py
```

### 3. 手动测试

```python
from src.dingtalk_service import init_dingtalk_service, send_test_notification

# 初始化服务
init_dingtalk_service("your_webhook_url", "your_secret")

# 发送测试消息
success = send_test_notification()
print(f"测试结果: {success}")
```

## 📱 通知类型

### 1. 客户端上线通知

当客户端连接到服务器时发送：

```
🟢 设备上线通知

设备ID: RK3588_001
IP地址: *************
时间: 2024-01-01 12:00:00

设备已成功连接到参数管理系统！
```

### 2. 客户端离线通知

当客户端断开连接时发送：

```
🔴 设备离线通知

设备ID: RK3588_001
IP地址: *************
时间: 2024-01-01 12:30:00

设备已从参数管理系统断开连接！
```

### 3. 测试通知

用于验证配置的测试消息：

```
🧪 钉钉通知测试

这是一条测试消息，用于验证钉钉机器人通知功能是否正常工作。

测试时间: 2024-01-01 12:00:00
系统: 参数管理系统

如果您收到此消息，说明钉钉通知配置成功！✅
```

## 🔧 高级配置

### 签名验证

为了提高安全性，建议启用签名验证：

1. 在创建钉钉机器人时选择"加签"
2. 将获得的密钥配置到 `DINGTALK_SECRET`
3. 系统会自动生成签名并验证请求

### 通知控制

- `DINGTALK_ENABLED`: 控制整个钉钉通知功能的开关
- `DINGTALK_CLIENT_ONLINE_ENABLED`: 单独控制客户端上线通知

## 🐛 故障排除

### 常见问题

1. **收不到通知**
   - 检查Webhook URL是否正确
   - 确认钉钉机器人是否被正确添加到群中
   - 检查网络连接

2. **签名验证失败**
   - 确认密钥配置正确
   - 检查系统时间是否准确

3. **配置不生效**
   - 重启服务器使配置生效
   - 检查日志文件中的错误信息

### 日志查看

查看服务器日志获取详细错误信息：

```bash
tail -f logs/server.log | grep -i dingtalk
```

## 📝 注意事项

1. 钉钉机器人有发送频率限制，请避免频繁发送消息
2. 建议在生产环境中启用签名验证
3. 定期检查机器人配置是否有效
4. 保护好Webhook URL和密钥，避免泄露

## 🔄 从短信通知迁移

如果您之前使用的是短信通知，现在可以：

1. 保留原有短信配置作为备用
2. 启用钉钉通知
3. 测试钉钉通知功能正常后，可以禁用短信通知

短信配置仍然保留在配置页面的"短信服务配置 (备用)"部分。
