#!/usr/bin/env python3
"""
钉钉集成测试脚本
用于验证钉钉通知功能是否正常工作
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from dingtalk_service import init_dingtalk_service, send_test_notification, send_client_online_notification
from config_manager import setup_config_manager

def test_dingtalk_integration():
    """测试钉钉集成功能"""
    print("开始测试钉钉集成功能...")
    
    # 1. 初始化配置管理器
    print("1. 初始化配置管理器...")
    config_manager = setup_config_manager()
    config = config_manager.get_config()
    
    # 2. 检查钉钉配置
    print("2. 检查钉钉配置...")
    dingtalk_enabled = config.get('DINGTALK_ENABLED', 'false').lower() == 'true'
    webhook_url = config.get('DINGTALK_WEBHOOK_URL', '')
    secret = config.get('DINGTALK_SECRET', '')
    client_online_enabled = config.get('DINGTALK_CLIENT_ONLINE_ENABLED', 'true').lower() == 'true'
    
    print(f"   钉钉通知启用: {dingtalk_enabled}")
    print(f"   Webhook URL: {webhook_url[:50]}..." if webhook_url else "   Webhook URL: 未配置")
    print(f"   密钥配置: {'已配置' if secret else '未配置'}")
    print(f"   客户端上线通知启用: {client_online_enabled}")
    
    if not dingtalk_enabled:
        print("⚠️  钉钉通知未启用，请在配置中启用后再测试")
        return False
    
    if not webhook_url:
        print("❌ Webhook URL未配置，无法进行测试")
        return False
    
    # 3. 初始化钉钉服务
    print("3. 初始化钉钉服务...")
    try:
        init_dingtalk_service(webhook_url, secret)
        print("✅ 钉钉服务初始化成功")
    except Exception as e:
        print(f"❌ 钉钉服务初始化失败: {e}")
        return False
    
    # 4. 测试发送测试通知
    print("4. 测试发送测试通知...")
    try:
        success = send_test_notification()
        if success:
            print("✅ 测试通知发送成功")
        else:
            print("❌ 测试通知发送失败")
            return False
    except Exception as e:
        print(f"❌ 发送测试通知异常: {e}")
        return False
    
    # 5. 测试客户端上线通知
    print("5. 测试客户端上线通知...")
    try:
        success = send_client_online_notification("TEST_CLIENT_001", "192.168.1.100")
        if success:
            print("✅ 客户端上线通知发送成功")
        else:
            print("❌ 客户端上线通知发送失败")
            return False
    except Exception as e:
        print(f"❌ 发送客户端上线通知异常: {e}")
        return False
    
    print("\n🎉 所有测试通过！钉钉集成功能正常工作。")
    return True

if __name__ == "__main__":
    print("钉钉集成测试")
    print("=" * 50)
    
    success = test_dingtalk_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试完成：钉钉集成功能正常")
        sys.exit(0)
    else:
        print("❌ 测试失败：请检查配置和网络连接")
        sys.exit(1)
